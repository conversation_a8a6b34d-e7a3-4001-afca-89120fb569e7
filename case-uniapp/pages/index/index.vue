<template>
  <view class="container">
    <!-- 优秀客户区域 -->
    <view class="excellent-section">
      <view class="section-title">优秀客户</view>
      <scroll-view class="excellent-scroll" scroll-x="true" show-scrollbar="false">
        <view class="excellent-list">
          <view class="excellent-item" v-for="user in excellentUsers" :key="user.userId"
            @click="goToUserProfile(user.userId)">
            <image class="user-avatar" :src="processAvatarUrl(user.avatar)" mode="aspectFill"></image>
            <text class="user-name">{{ user.nickName }}</text>
            <view class="view-btn">查看</view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 成功案例区域 -->
    <view class="case-section">
      <view class="case-header">
        <text class="section-title">成功案例</text>
        <view class="tab-switch">
          <text class="tab-item" :class="{ active: currentTab === 'recommended' }"
            @click="switchTab('recommended')">推荐</text>
          <text class="tab-item" :class="{ active: currentTab === 'latest' }" @click="switchTab('latest')">最新</text>
        </view>
      </view>

      <!-- 案例列表 -->
      <view class="case-list">
        <view class="case-item" v-for="caseItem in caseList" :key="caseItem.caseId">
          <!-- 用户信息 -->
          <view class="case-user" @click="goToUserProfile(caseItem.publisherId)">
            <image class="user-avatar-small" :src="processAvatarUrl(caseItem.publisher.avatar)" mode="aspectFill">
            </image>
            <view class="user-info">
              <text class="user-name-small">{{ caseItem.publisher.nickName }}</text>
              <text class="publish-time">{{ formatTime(caseItem.createTime) }}</text>
            </view>
          </view>

          <!-- 案例标签 -->
          <view class="case-tags" v-if="caseItem.caseTags">
            <text class="tag" v-for="tag in processTags(caseItem.caseTags)" :key="tag">#{{ tag }}</text>
          </view>

          <!-- 案例标题 -->
          <view class="case-title" @click="goToCaseDetail(caseItem.caseId)">
            {{ caseItem.caseTitle }}
          </view>

          <!-- 案例内容 -->
          <view class="case-content">
            <view class="content-text" :class="{ expanded: expandedCases[caseItem.caseId] }">
              {{ processCaseContent(caseItem.caseContent, expandedCases[caseItem.caseId] ? 1000 : 150) }}
            </view>
            <text class="expand-btn" v-if="processCaseContent(caseItem.caseContent, 1000).length > 150"
              @click="toggleExpand(caseItem.caseId)">
              {{ expandedCases[caseItem.caseId] ? '收起' : '全文' }}
            </text>
          </view>

          <!-- 案例图片 -->
          <view class="case-images" v-if="caseItem.caseImages">
            <view class="image-grid">
              <view class="image-item" v-for="(image, index) in processImages(caseItem.caseImages).slice(0, 3)"
                :key="index" @click="openImageViewer(processImages(caseItem.caseImages), index)">
                <image class="case-image" :src="image" mode="aspectFill"></image>
                <!-- 显示剩余图片数量 -->
                <view class="more-images" v-if="index === 2 && processImages(caseItem.caseImages).length > 3">
                  <text class="more-count">+{{ processImages(caseItem.caseImages).length - 3 }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 案例操作 -->
          <view class="case-actions">
            <view class="action-item" @click="goToCaseDetail(caseItem.caseId)">
              <text class="action-text">查看详情</text>
            </view>
            <view class="action-item">
              <text class="click-count">{{ caseItem.clickCount }} 次查看</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore && !loadingMore">
        <text class="load-text" @click="loadMore">加载更多</text>
      </view>

      <!-- 加载更多中 -->
      <view class="load-more" v-if="loadingMore">
        <text class="load-text loading">加载中...</text>
      </view>

      <!-- 没有更多数据 -->
      <view class="no-more" v-if="!hasMore && caseList.length > 0">
        <text class="no-more-text">没有更多数据了</text>
      </view>
    </view>

    <!-- 图片查看器 -->
    <ImageViewer :visible="imageViewerVisible" :images="currentImages" :initialIndex="currentImageIndex"
      @close="closeImageViewer" @change="onImageChange" />
  </view>
</template>

<script>
import { caseUserApi, caseInfoApi } from '@/utils/api.js'
import { formatTime, processImages, processAvatarUrl, processTags, processCaseContent, previewImages } from '@/utils/utils.js'
import ImageViewer from '@/components/ImageViewer.vue'

export default {
  components: {
    ImageViewer
  },
  data() {
    return {
      // 优秀客户列表
      excellentUsers: [],
      // 当前选中的标签页
      currentTab: 'recommended',
      // 案例列表
      caseList: [],
      // 展开的案例内容
      expandedCases: {},
      // 是否还有更多数据
      hasMore: true,
      // 当前页码
      currentPage: 1,
      // 每页数量
      pageSize: 10,
      // 加载状态
      loading: false,
      // 加载更多状态
      loadingMore: false,
      // 图片查看器相关
      imageViewerVisible: false,
      currentImages: [],
      currentImageIndex: 0
    }
  },

  onLoad() {
    this.initData()
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  onReachBottom() {
    this.loadMore()
  },

  methods: {
    // 初始化数据
    async initData() {
      await this.loadExcellentUsers()
      await this.loadCaseList()
    },

    // 刷新数据
    async refreshData() {
      this.currentPage = 1
      this.hasMore = true
      this.caseList = []
      this.expandedCases = {}

      await this.loadExcellentUsers()
      await this.loadCaseList()

      uni.stopPullDownRefresh()
    },

    // 加载优秀客户
    async loadExcellentUsers() {
      try {
        const res = await caseUserApi.getExcellentUsers()
        this.excellentUsers = res.data || []
      } catch (error) {
        console.error('加载优秀客户失败:', error)
      }
    },

    // 加载案例列表
    async loadCaseList() {
      // 如果是第一页加载，设置loading状态；如果是加载更多，不设置loading状态
      const isFirstPage = this.currentPage === 1
      if (isFirstPage && this.loading) return
      if (!isFirstPage && this.loadingMore) return

      if (isFirstPage) {
        this.loading = true
      }

      try {
        let res
        if (this.currentTab === 'recommended') {
          res = await caseInfoApi.getRecommendedCases(this.currentPage, this.pageSize)
        } else {
          res = await caseInfoApi.getLatestCases(this.currentPage, this.pageSize)
        }

        const newCases = res.data || []
        if (this.currentPage === 1) {
          this.caseList = newCases
        } else {
          this.caseList = [...this.caseList, ...newCases]
        }

        // 判断是否还有更多数据：如果返回的数据量小于请求的页面大小，说明没有更多数据了
        this.hasMore = newCases.length >= this.pageSize

        console.log(`加载${isFirstPage ? '第一页' : '更多'}数据完成，当前页码: ${this.currentPage}, 获取数据量: ${newCases.length}, 是否还有更多: ${this.hasMore}`)
      } catch (error) {
        console.error('加载案例列表失败:', error)
        // 出错时停止加载更多
        this.hasMore = false
        throw error // 重新抛出错误，让调用方处理
      } finally {
        if (isFirstPage) {
          this.loading = false
        }
      }
    },

    // 切换标签页
    async switchTab(tab) {
      if (this.currentTab === tab) return

      this.currentTab = tab
      this.currentPage = 1
      this.hasMore = true
      this.caseList = []
      this.expandedCases = {}

      await this.loadCaseList()
    },

    // 加载更多
    async loadMore() {
      if (!this.hasMore || this.loading || this.loadingMore) return

      console.log('加载更多数据，当前页码:', this.currentPage, '下一页:', this.currentPage + 1)
      this.loadingMore = true
      this.currentPage++

      try {
        await this.loadCaseList()
      } catch (error) {
        // 如果加载失败，回退页码
        this.currentPage--
        console.error('加载更多失败:', error)
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        })
      } finally {
        this.loadingMore = false
      }
    },

    // 切换内容展开状态
    toggleExpand(caseId) {
      this.$set(this.expandedCases, caseId, !this.expandedCases[caseId])
    },

    // 跳转到用户主页
    goToUserProfile(userId) {
      uni.navigateTo({
        url: `/pages/user/profile?userId=${userId}`
      })
    },

    // 跳转到案例详情
    goToCaseDetail(caseId) {
      uni.navigateTo({
        url: `/pages/case/detail?caseId=${caseId}`
      })
    },

    // 打开图片查看器
    openImageViewer(images, index = 0) {
      this.currentImages = images
      this.currentImageIndex = index
      this.imageViewerVisible = true
    },

    // 关闭图片查看器
    closeImageViewer() {
      this.imageViewerVisible = false
      this.currentImages = []
      this.currentImageIndex = 0
    },

    // 图片切换事件
    onImageChange(index) {
      this.currentImageIndex = index
    },

    // 工具方法
    formatTime,
    processImages,
    processAvatarUrl,
    processTags,
    processCaseContent,
    previewImages
  }
}
</script>

<style scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 优秀客户区域 */
.excellent-section {
  background-color: #fff;
  padding: 30rpx 0 20rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  padding: 0 30rpx 20rpx;
}

.excellent-scroll {
  white-space: nowrap;
}

.excellent-list {
  display: flex;
  padding: 0 20rpx;
}

.excellent-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 10rpx;
  min-width: 120rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-bottom: 10rpx;
}

.user-name {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  text-align: center;
}

.view-btn {
  background-color: #007AFF;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

/* 成功案例区域 */
.case-section {
  background-color: #fff;
  padding: 30rpx;
}

.case-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.tab-switch {
  display: flex;
}

.tab-item {
  font-size: 28rpx;
  color: #999;
  margin-left: 30rpx;
  padding-bottom: 5rpx;
  position: relative;
}

.tab-item.active {
  color: #007AFF;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background-color: #007AFF;
  border-radius: 2rpx;
}

/* 案例列表 */
.case-list {
  margin-bottom: 30rpx;
}

.case-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.case-user {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-avatar-small {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name-small {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 5rpx;
}

.publish-time {
  font-size: 24rpx;
  color: #999;
}

.case-tags {
  margin-bottom: 15rpx;
}

.tag {
  display: inline-block;
  background-color: #f0f8ff;
  color: #007AFF;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 10rpx;
  margin-bottom: 8rpx;
}

.case-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 15rpx;
}

.case-content {
  margin-bottom: 20rpx;
}

.content-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-text:not(.expanded) {
  -webkit-line-clamp: 3;
}

.expand-btn {
  color: #007AFF;
  font-size: 26rpx;
  margin-top: 10rpx;
  display: inline-block;
}

/* 案例图片 */
.case-images {
  margin-bottom: 20rpx;
}

.image-grid {
  display: flex;
  gap: 10rpx;
}

.image-item {
  position: relative;
  flex: 1;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.case-image {
  width: 100%;
  height: 100%;
}

.more-images {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-count {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
}

/* 案例操作 */
.case-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-item {
  display: flex;
  align-items: center;
}

.action-text {
  color: #007AFF;
  font-size: 26rpx;
}

.click-count {
  color: #999;
  font-size: 24rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
}

.load-text {
  color: #007AFF;
  font-size: 28rpx;
}

.load-text.loading {
  color: #999;
}

.no-more {
  text-align: center;
  padding: 30rpx 0;
}

.no-more-text {
  color: #999;
  font-size: 26rpx;
}
</style>
